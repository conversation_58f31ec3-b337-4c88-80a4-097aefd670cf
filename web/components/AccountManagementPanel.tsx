"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { X, LogIn, CheckCircle, AlertCircle, RefreshCw, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

interface AccountManagementPanelProps {
  onClose: () => void
}

interface LoginStatus {
  platform: 'taobao' | 'xiaohongshu'
  isLoggedIn: boolean
  lastLoginTime?: string
  sessionValid?: boolean
  fileExists?: boolean
  error?: string
}

export function AccountManagementPanel({ onClose }: AccountManagementPanelProps) {
  const [loginStatuses, setLoginStatuses] = useState<LoginStatus[]>([
    { platform: 'taobao', isLoggedIn: false },
    { platform: 'xia<PERSON><PERSON><PERSON>', isLoggedIn: false }
  ])
  const [isLoading, setIsLoading] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { toast } = useToast()

  // 获取平台显示名称
  const getPlatformDisplayName = (platform: string) => {
    return platform === 'taobao' ? '淘宝' : '小红书'
  }

  // 获取登录状态
  const fetchLoginStatuses = async () => {
    setIsRefreshing(true)
    try {
      // 调用主进程检查登录状态（包含API验证）
      console.log('[AccountManagement] 🔍 开始检查登录状态（包含API验证）...')
      const statuses = await window.electronAPI?.checkLoginStatus?.() || []
      console.log('[AccountManagement] ✅ 登录状态检查完成:', statuses)
      // 🔥 V2.5: 类型转换，确保platform是正确的联合类型
      const typedStatuses = statuses.filter(s =>
        s.platform === 'taobao' || s.platform === 'xiaohongshu'
      ) as LoginStatus[]
      setLoginStatuses(typedStatuses)

      // 显示验证结果
      const invalidCount = statuses.filter(s => s.fileExists && !s.isLoggedIn).length
      if (invalidCount > 0) {
        toast({
          title: "登录状态已更新",
          description: `检测到 ${invalidCount} 个平台的登录已失效，状态已自动更新`,
          variant: "default"
        })
      }
    } catch (error) {
      console.error('Failed to fetch login statuses:', error)
      toast({
        title: "获取登录状态失败",
        description: "无法检查当前登录状态，请稍后重试",
        variant: "destructive"
      })
    } finally {
      setIsRefreshing(false)
    }
  }

  // 启动登录流程
  const handleLogin = async (platform: 'taobao' | 'xiaohongshu') => {
    setIsLoading(true)
    try {
      toast({
        title: "启动登录流程",
        description: `正在为${getPlatformDisplayName(platform)}启动登录窗口...`
      })

      // 调用主进程启动登录会话
      const result = await window.electronAPI?.startLoginSession?.(platform)
      
      if (result?.success) {
        toast({
          title: "登录成功",
          description: `${getPlatformDisplayName(platform)}登录成功！`,
          variant: "default"
        })
        
        // 刷新登录状态
        await fetchLoginStatuses()
      } else {
        toast({
          title: "登录失败",
          description: result?.error || `${getPlatformDisplayName(platform)}登录失败`,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Login failed:', error)
      toast({
        title: "登录失败",
        description: `${getPlatformDisplayName(platform)}登录过程中出现错误`,
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 清除登录状态
  const handleLogout = async (platform: 'taobao' | 'xiaohongshu') => {
    try {
      // 调用主进程清除登录状态
      await window.electronAPI?.clearLoginSession?.(platform)
      
      toast({
        title: "已退出登录",
        description: `${getPlatformDisplayName(platform)}登录状态已清除`
      })
      
      // 刷新登录状态
      await fetchLoginStatuses()
    } catch (error) {
      console.error('Logout failed:', error)
      toast({
        title: "退出失败",
        description: `清除${getPlatformDisplayName(platform)}登录状态失败`,
        variant: "destructive"
      })
    }
  }

  // 组件加载时获取登录状态
  useEffect(() => {
    fetchLoginStatuses()

    // 监听登录状态同步事件
    const handleLoginStatesUpdated = (statuses: LoginStatus[]) => {
      console.log('[AccountManagement] 📡 收到登录状态更新:', statuses)
      setLoginStatuses(statuses)
    }

    const handleLoginStatesSynced = (syncInfo: { timestamp: string; successCount: number; failCount: number }) => {
      console.log('[AccountManagement] 🔄 登录状态同步完成:', syncInfo)
      toast({
        title: "登录状态已同步",
        description: `成功同步 ${syncInfo.successCount} 个平台的登录状态`,
        duration: 3000,
      })
      // 同步完成后重新获取最新状态
      fetchLoginStatuses()
    }

    // 添加事件监听器
    if (window.electronAPI?.on) {
      window.electronAPI.on('login-states-updated', handleLoginStatesUpdated)
      window.electronAPI.on('login-states-synced', handleLoginStatesSynced)
    }

    // 清理事件监听器
    return () => {
      // 🔥 V2.5: 使用removeAllListeners替代removeListener
      if (window.electronAPI?.removeAllListeners) {
        window.electronAPI.removeAllListeners('login-states-updated')
        window.electronAPI.removeAllListeners('login-states-synced')
      }
    }
  }, [])

  return (
    <div className="p-6">
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">账号管理</h2>
        </div>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* 刷新按钮 */}
      <div className="flex justify-between items-center mb-4">
        <div>
          <h3 className="text-sm font-medium text-gray-700">登录状态</h3>
          <p className="text-xs text-gray-500">点击刷新将通过API验证登录有效性</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={fetchLoginStatuses}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <RefreshCw className="h-4 w-4 mr-2" />
          )}
          {isRefreshing ? '验证中...' : '刷新验证'}
        </Button>
      </div>

      {/* 平台登录状态卡片 */}
      <div className="space-y-4">
        {loginStatuses.map((status) => (
          <Card key={status.platform} className="border">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">
                  {getPlatformDisplayName(status.platform)}
                </CardTitle>
                <Badge 
                  variant={status.isLoggedIn ? "default" : "secondary"}
                  className={status.isLoggedIn ? "bg-green-100 text-green-800" : ""}
                >
                  {status.isLoggedIn ? (
                    <><CheckCircle className="h-3 w-3 mr-1" />已登录</>
                  ) : (
                    <><AlertCircle className="h-3 w-3 mr-1" />未登录</>
                  )}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {status.isLoggedIn && status.lastLoginTime && (
                <p className="text-xs text-gray-500 mb-3">
                  最后登录：{new Date(status.lastLoginTime).toLocaleString()}
                </p>
              )}

              {status.fileExists && !status.isLoggedIn && (
                <p className="text-xs text-orange-600 mb-3">
                  ⚠️ 登录状态已失效，已自动清除
                </p>
              )}

              {status.error && (
                <p className="text-xs text-red-600 mb-3">
                  错误：{status.error}
                </p>
              )}

              <div className="flex gap-2">
                {status.isLoggedIn ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => handleLogout(status.platform)}
                    className="text-red-600 hover:text-red-700"
                  >
                    退出登录
                  </Button>
                ) : (
                  <Button 
                    size="sm"
                    onClick={() => handleLogin(status.platform)}
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <LogIn className="h-4 w-4 mr-2" />
                    )}
                    立即登录
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Separator className="my-6" />

      {/* 使用说明 */}
      <div className="bg-blue-50 rounded-lg p-4">
        <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明</h4>
        <ul className="text-xs text-blue-800 space-y-1">
          <li>• 点击"立即登录"将打开浏览器窗口，请按照提示完成登录</li>
          <li>• 登录成功后，系统会自动保存登录状态</li>
          <li>• 如果任务中途需要登录，系统会自动触发登录流程</li>
          <li>• 建议在开始任务前先完成所有平台的登录</li>
        </ul>
      </div>
    </div>
  )
}
