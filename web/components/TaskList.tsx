"use client"

import React, { useEffect, useCallback } from "react"
import { useRouter, useParams } from "next/navigation"
import { TaskItem } from "./TaskItem"
import { useTaskStore } from "@/hooks/useTaskStore"

export function TaskList() {
  const { tasks, isLoading, isEmpty, debouncedRefresh, hasTask } = useTaskStore()
  const router = useRouter()
  const params = useParams()

  const handleTaskDeleted = useCallback((deletedTaskId: string) => {
    // 检查删除的任务是否是当前活动任务
    const deletedTask = tasks.find(task => task.id === deletedTaskId)
    if (deletedTask && params.id === deletedTask.jobId) {
      console.log(`[TaskList] Deleted task ${deletedTaskId} matches current route ${params.id}. Navigating to home.`)
      router.push('/app')
    }

    // 任务删除后，触发状态刷新（防抖）
    debouncedRefresh(200)
  }, [params.id, router, tasks, debouncedRefresh])

  // 🔧 简化的页面可见性处理
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log(`[TaskList] 👁️ Page became visible, refreshing tasks...`)
        debouncedRefresh(100) // 防抖刷新
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [debouncedRefresh])

  // 🔧 智能路由检查：只在必要时导航
  useEffect(() => {
    const currentTaskId = params.id as string
    if (currentTaskId && !isLoading && !hasTask(currentTaskId)) {
      console.log(`[TaskList] Task ${currentTaskId} not found. Navigating to home.`)
      router.push('/app')
    }
  }, [params.id, isLoading, hasTask, router])

  if (isLoading) {
    return <div className="text-center text-sm text-gray-500 py-4">正在加载任务...</div>
  }

  if (isEmpty) {
    return <div className="text-center text-sm text-gray-500 py-4">暂无分析任务</div>
  }

  return (
    <div className="space-y-1">
      {tasks.map((task) => (
        <TaskItem key={task.id} task={task} onTaskDeleted={handleTaskDeleted} />
      ))}
    </div>
  )
}
