"use client"

import React, { useState, useEffect } from "react"
import { Shield, LogIn, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"

interface LoginSuggestionCardProps {
  className?: string
}

export function LoginSuggestionCard({ className = "" }: LoginSuggestionCardProps) {
  const [loginStatuses, setLoginStatuses] = useState<Array<{
    platform: string;
    isLoggedIn: boolean;
    lastLoginTime?: string;
  }>>([])
  const [showLoginSuggestion, setShowLoginSuggestion] = useState(false)
  const [isCheckingLogin, setIsCheckingLogin] = useState(false)
  
  const { toast } = useToast()

  // 检查登录状态（优化：使用缓存机制，避免频繁触发浏览器验证）
  const checkLoginStatus = async (forceCheck = false) => {
    try {
      // 🚀 修复：只有在强制检查时才调用API，否则使用本地缓存
      if (forceCheck) {
        const statuses = await window.electronAPI?.checkLoginStatus?.() || []
        setLoginStatuses(statuses)

        // 检查是否有平台未登录
        const hasUnloggedPlatform = statuses.some(status => !status.isLoggedIn)
        setShowLoginSuggestion(hasUnloggedPlatform)
      } else {
        // 🚀 修复：轻量级检查 - 监听后端状态更新，而不是直接隐藏
        console.log('[LoginSuggestionCard] 🔄 使用轻量级登录状态检查...')
        // 不立即隐藏，等待后端状态更新事件
        // setShowLoginSuggestion(false) // 删除这行，让组件响应后端状态
      }
    } catch (error) {
      console.error('Failed to check login status:', error)
    }
  }

  // 快速登录功能
  const handleQuickLogin = async (platform: 'taobao' | 'xiaohongshu') => {
    setIsCheckingLogin(true)
    try {
      toast({
        title: "启动登录",
        description: `正在为${platform === 'taobao' ? '淘宝' : '小红书'}启动登录窗口...`
      })

      const result = await window.electronAPI?.startLoginSession?.(platform)
      
      if (result?.success) {
        toast({
          title: "登录成功",
          description: `${platform === 'taobao' ? '淘宝' : '小红书'}登录成功！`,
          variant: "default"
        })
        await checkLoginStatus(true) // 刷新登录状态（强制检查）
      } else {
        toast({
          title: "登录失败",
          description: result?.error || "登录过程中出现错误",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Quick login failed:', error)
      toast({
        title: "登录失败",
        description: "登录过程中出现错误",
        variant: "destructive"
      })
    } finally {
      setIsCheckingLogin(false)
    }
  }

  // 🚀 修复：组件加载时使用轻量级检查，避免触发不必要的浏览器验证
  useEffect(() => {
    checkLoginStatus(false) // 使用轻量级检查

    // 🔧 新增：监听登录状态更新事件
    const handleLoginStatesUpdated = (event: any, statuses: Array<{
      platform: string;
      isLoggedIn: boolean;
      lastLoginTime?: string;
      sessionValid?: boolean;
      fileExists?: boolean;
    }>) => {
      console.log('[LoginSuggestionCard] 📡 收到登录状态更新:', statuses)
      setLoginStatuses(statuses)

      // 检查是否有平台未登录
      const hasUnloggedPlatform = statuses.some(status => !status.isLoggedIn)
      setShowLoginSuggestion(hasUnloggedPlatform)

      if (hasUnloggedPlatform) {
        console.log('[LoginSuggestionCard] ⚠️ 检测到未登录平台，显示登录建议')
      }
    }

    // 🔧 新增：监听登录失效通知
    const handleLoginRequired = (event: any, data: { platforms: string[], message: string }) => {
      console.log('[LoginSuggestionCard] ⚠️ 收到登录失效通知:', data)

      // 显示登录建议卡片
      setShowLoginSuggestion(true)

      // 更新登录状态，标记失效的平台
      setLoginStatuses(prev => {
        const updated = [...prev]
        data.platforms.forEach(platform => {
          const index = updated.findIndex(s => s.platform === platform)
          if (index >= 0) {
            updated[index] = { ...updated[index], isLoggedIn: false }
          } else {
            updated.push({ platform, isLoggedIn: false })
          }
        })
        return updated
      })

      // 显示提示消息
      toast({
        title: "登录已失效",
        description: data.message,
        variant: "destructive",
        duration: 8000
      })
    }

    // 🔧 新增：监听登录状态同步完成
    const handleLoginStatesSynced = (event: any, data: { needsRelogin?: boolean, loginFailures?: string[] }) => {
      if (data.needsRelogin && data.loginFailures && data.loginFailures.length > 0) {
        console.log('[LoginSuggestionCard] ⚠️ 登录状态同步发现失效平台:', data.loginFailures)
        setShowLoginSuggestion(true)
      }
    }

    // 注册事件监听器
    if (window.electronAPI?.on) {
      window.electronAPI.on('login-states-updated', handleLoginStatesUpdated)
      window.electronAPI.on('login-required', handleLoginRequired)
      window.electronAPI.on('login-states-synced', handleLoginStatesSynced)
    }

    // 清理事件监听器
    return () => {
      if (window.electronAPI?.off) {
        window.electronAPI.off('login-states-updated', handleLoginStatesUpdated)
        window.electronAPI.off('login-required', handleLoginRequired)
        window.electronAPI.off('login-states-synced', handleLoginStatesSynced)
      }
    }
  }, [])

  if (!showLoginSuggestion) {
    return null
  }

  return (
    <div className={`mb-6 relative overflow-hidden rounded-lg animate-gentle-breathe ${className}`}>
      {/* 适度的光影动效 */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent w-1/3 h-full transform -skew-x-12 animate-shimmer z-10 pointer-events-none"></div>

      <div className="relative p-4 bg-gradient-to-r from-amber-50/95 to-amber-100/95 border border-amber-200/80 rounded-lg backdrop-blur-sm">
        <div className="flex items-start gap-3">
          <Shield className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div className="flex-1">
            <p className="text-sm text-amber-700 mb-3">
              检测到有平台未登录，请先登录以免任务执行中断。账号信息会储存在本地，您随时可在平台账号管理中退出。
            </p>
            <div className="flex items-center gap-2 flex-wrap">
              {loginStatuses
                .filter(status => !status.isLoggedIn)
                .map(status => (
                  <Button
                    key={status.platform}
                    type="button"
                    size="sm"
                    variant="outline"
                    onClick={() => handleQuickLogin(status.platform as 'taobao' | 'xiaohongshu')}
                    disabled={isCheckingLogin}
                    className="h-8 px-3 text-xs border-amber-300 hover:bg-amber-100 text-amber-700"
                  >
                    {isCheckingLogin ? (
                      <Loader2 className="w-4 h-4 animate-spin mr-1" />
                    ) : (
                      <LogIn className="w-4 h-4 mr-1" />
                    )}
                    登录{status.platform === 'taobao' ? '淘宝' : '小红书'}
                  </Button>
                ))}
              <Button
                type="button"
                size="sm"
                variant="ghost"
                onClick={() => setShowLoginSuggestion(false)}
                className="h-8 px-3 text-xs text-amber-600 hover:bg-amber-100"
              >
                稍后登录
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
